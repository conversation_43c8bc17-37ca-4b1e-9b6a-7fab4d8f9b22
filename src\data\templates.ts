import { PageFormat, PageOrientation } from "@/utils/pageUtils";

export interface Logo {
  src: string;
  x: number;
  y: number;
  width: number;
  height: number;
}

export interface VisibilityCondition {
  fieldId: string;
  operator: 'equals' | 'not_equals' | 'greater_than' | 'less_than' | 'contains';
  value: string;
}

export interface ValidationRule {
  required?: boolean;
  minLength?: number;
  maxLength?: number;
  minValue?: number;
  maxValue?: number;
}

export interface FormField {
  id: string;
  label: string;
  type: "text" | "textarea" | "select" | "radio" | "checkbox" | "signature" | "static-text" | "table" | "date" | "number" | "email" | "image" | "box" | "line" | "arrow" | "qrcode";
  x: number;
  y: number;
  width: number;
  height: number;
  placeholder?: string;
  options?: string[];
  defaultValue?: string | boolean;
  // Styling for static-text and box
  fontSize?: number;
  fontWeight?: "normal" | "bold";
  fontStyle?: "normal" | "italic";
  textAlign?: "left" | "center" | "right";
  color?: string; // Fill color for box, text color for static-text
  borderColor?: string;
  borderWidth?: number;
  borderRadius?: number;
  // Properties for arrow
  arrowDirection?: 'up' | 'down' | 'left' | 'right';
  arrowheadSize?: number;
  // Properties for table
  columns?: TableColumn[];
  summary?: TableSummary[];
  // Grouping
  groupId?: string;
  // Conditional Logic
  visibilityCondition?: VisibilityCondition;
  // Validation
  validation?: ValidationRule;
  // Editor-specific state
  isVisible?: boolean;
  isLocked?: boolean;
}

export interface TableColumn {
  id: string;
  header: string;
  calculation?: string; // e.g., "quantity * price"
}

export interface TableSummary {
  id: string;
  label: string;
  formula: string; // e.g., "SUM(total)"
}

export interface Watermark {
  text: string;
  color: string;
  opacity: number;
  fontSize: number;
  rotation: number;
  enabled: boolean;
}

export interface DesignSettings {
  primaryColor: string;
  textColor: string;
  accentColor: string;
  fontFamily: string;
  showTitle?: boolean;
}

export const defaultDesignSettings: DesignSettings = {
  primaryColor: "#111827",
  textColor: "#374151",
  accentColor: "#e5e7eb",
  fontFamily: "Helvetica",
  showTitle: true,
};

export interface PageSetup {
  format: PageFormat;
  orientation: PageOrientation;
}

export const defaultPageSetup: PageSetup = {
  format: 'letter',
  orientation: 'portrait',
};

export interface Page {
  id: string;
  fields: FormField[];
  backgroundImage?: {
    src: string;
    opacity: number;
  };
}

export interface Template {
  title: string;
  description: string;
  category: string;
  subcategory?: string;
  pages: Page[];
  designSettings?: DesignSettings;
  pageSetup?: PageSetup;
  watermark?: Watermark;
  isCustom?: boolean;
  // Deprecated, for migration
  fields?: FormField[];
}

const migrateTemplate = (template: Template): Template => {
    if (template.fields && !template.pages) {
        template.pages = [{ id: `page_${Date.now()}`, fields: template.fields }];
        delete template.fields;
    }
    return template;
}

export const templates: Template[] = [
  {
    title: "Simple Invoice",
    description: "A clean and simple invoice for freelancers and small businesses.",
    category: "Invoices",
    pageSetup: { format: 'letter', orientation: 'portrait' },
    pages: [{
      id: "page_1",
      fields: [
        { id: "invoice_title", label: "Invoice Title", type: "static-text", x: 40, y: 70, width: 200, height: 30, defaultValue: "INVOICE", fontSize: 28, fontWeight: "bold" },
        { id: "bill_to_label", label: "Bill To Label", type: "static-text", x: 40, y: 120, width: 100, height: 20, defaultValue: "BILL TO:", fontWeight: "bold" },
        { id: "client_name", label: "Client Name", type: "text", x: 40, y: 140, width: 200, height: 30, placeholder: "Client Name", validation: { required: true } },
        { id: "client_address", label: "Client Address", type: "textarea", x: 40, y: 175, width: 200, height: 60, placeholder: "Client Address" },
        { id: "invoice_num_label", label: "Invoice # Label", type: "static-text", x: 350, y: 120, width: 100, height: 20, defaultValue: "Invoice #:", textAlign: "right" },
        { id: "invoice_num", label: "Invoice Number", type: "number", x: 455, y: 115, width: 100, height: 30, placeholder: "1001", validation: { minValue: 1 } },
        { id: "date_label", label: "Date Label", type: "static-text", x: 350, y: 145, width: 100, height: 20, defaultValue: "Date:", textAlign: "right" },
        { id: "invoice_date", label: "Invoice Date", type: "date", x: 455, y: 140, width: 100, height: 30, placeholder: "Jan 1, 2024" },
        { id: "invoice_table", label: "Invoice Items", type: "table", x: 40, y: 250, width: 532, height: 300,
          columns: [
            { id: "item", header: "Item Description" },
            { id: "quantity", header: "Quantity" },
            { id: "price", header: "Unit Price" },
            { id: "total", header: "Total", calculation: "quantity * price" },
          ],
          summary: [
            { id: "subtotal", label: "Subtotal", formula: "SUM(total)" },
            { id: "tax", label: "Tax (10%)", formula: "SUM(total) * 0.1" },
            { id: "grand_total", label: "Total", formula: "SUM(total) * 1.1" },
          ]
        },
        { id: "notes_label", label: "Notes Label", type: "static-text", x: 40, y: 600, width: 100, height: 20, defaultValue: "Notes:", fontWeight: "bold" },
        { id: "notes_content", label: "Notes", type: "textarea", x: 40, y: 620, width: 300, height: 60, placeholder: "Thank you for your business." },
        { id: "signature_field", label: "Signature", type: "signature", x: 380, y: 680, width: 180, height: 60, placeholder: "Sign here" },
        { id: "overdue_notice", label: "Overdue Notice", type: "static-text", x: 350, y: 80, width: 200, height: 20, defaultValue: "PAYMENT OVERDUE", color: "#EF4444", fontWeight: "bold",
          visibilityCondition: { fieldId: "invoice_num", operator: "less_than", value: "1000" }
        },
      ]
    }],
  },
  {
    title: "Service Agreement",
    description: "A standard contract for providing services to a client.",
    category: "Contracts",
    pageSetup: { format: 'letter', orientation: 'portrait' },
    pages: [{
      id: "page_2",
      fields: [
        { id: "agreement_title", label: "Title", type: "static-text", x: 40, y: 40, width: 532, height: 40, defaultValue: "Service Agreement", fontSize: 32, fontWeight: "bold", textAlign: "center" },
        { id: "agreement_intro", label: "Introduction", type: "static-text", x: 40, y: 100, width: 532, height: 80, defaultValue: "This Service Agreement is made and entered into as of {{agreement_date}} by and between {{company_name}} ('Service Provider') and {{client_name}} ('Client').", fontSize: 12 },
        { id: "agreement_date", label: "Agreement Date", type: "date", x: 40, y: 190, width: 250, height: 30, placeholder: "Select a date" },
        { id: "company_name", label: "Service Provider Name", type: "text", x: 322, y: 190, width: 250, height: 30, placeholder: "Your Company Name", defaultValue: "Your Company Inc." },
        { id: "client_name", label: "Client Name", type: "text", x: 40, y: 230, width: 250, height: 30, placeholder: "Client Full Name" },
        { id: "services_rendered", label: "Services Rendered", type: "textarea", x: 40, y: 280, width: 532, height: 100, placeholder: "Describe the services to be provided..." },
        { id: "payment_terms", label: "Payment Terms", type: "text", x: 40, y: 400, width: 532, height: 30, placeholder: "e.g., Net 30, 50% upfront" },
        { id: "client_signature", label: "Client Signature", type: "signature", x: 40, y: 480, width: 250, height: 60 },
        { id: "provider_signature", label: "Provider Signature", type: "signature", x: 322, y: 480, width: 250, height: 60 },
      ]
    }],
  },
  {
    title: "Job Application",
    description: "A form for applicants to fill out when applying for a job.",
    category: "HR",
    pageSetup: { format: 'letter', orientation: 'portrait' },
    pages: [{
      id: "page_3",
      fields: [
        { id: "app_title", label: "Title", type: "static-text", x: 40, y: 40, width: 532, height: 30, defaultValue: "Application for Employment", fontSize: 24, fontWeight: "bold" },
        { id: "full_name", label: "Full Name", type: "text", x: 40, y: 90, width: 250, height: 30, placeholder: "First and Last Name" },
        { id: "email", label: "Email Address", type: "email", x: 322, y: 90, width: 250, height: 30, placeholder: "<EMAIL>" },
        { id: "position_applying_for", label: "Position Applying For", type: "text", x: 40, y: 140, width: 532, height: 30, placeholder: "e.g., Software Engineer" },
        { id: "start_date", label: "Available Start Date", type: "date", x: 40, y: 190, width: 250, height: 30, placeholder: "MM/DD/YYYY" },
        { id: "employment_type", label: "Desired Employment Type", type: "select", x: 322, y: 190, width: 250, height: 30, options: ["Full-time", "Part-time", "Contract", "Internship"] },
        { id: "cover_letter", label: "Cover Letter", type: "textarea", x: 40, y: 240, width: 532, height: 200, placeholder: "Tell us why you're a great fit..." },
        { id: "applicant_signature", label: "Applicant Signature", type: "signature", x: 40, y: 460, width: 250, height: 60 },
      ]
    }],
  },
  {
    title: "User Profile Card",
    description: "A simple card to display user profile information, including a photo.",
    category: "HR",
    subcategory: "Profiles",
    pageSetup: { format: 'letter', orientation: 'portrait' },
    pages: [{
      id: "page_4",
      fields: [
        { id: "profile_picture", label: "Profile Picture", type: "image", x: 40, y: 40, width: 150, height: 150 },
        { id: "full_name", label: "Full Name", type: "static-text", x: 220, y: 60, width: 352, height: 30, defaultValue: "Alex Doe", fontSize: 28, fontWeight: "bold" },
        { id: "job_title", label: "Job Title", type: "static-text", x: 220, y: 100, width: 352, height: 20, defaultValue: "Software Engineer", fontSize: 18, color: "#6b7280" },
        { id: "email_label", label: "Email Label", type: "static-text", x: 220, y: 140, width: 80, height: 20, defaultValue: "Email:", fontWeight: "bold" },
        { id: "email_value", label: "Email Value", type: "text", x: 310, y: 135, width: 262, height: 30, placeholder: "<EMAIL>" },
        { id: "phone_label", label: "Phone Label", type: "static-text", x: 220, y: 170, width: 80, height: 20, defaultValue: "Phone:", fontWeight: "bold" },
        { id: "phone_value", label: "Phone Value", type: "text", x: 310, y: 165, width: 262, height: 30, placeholder: "(*************" },
        { id: "bio", label: "Biography", type: "textarea", x: 40, y: 220, width: 532, height: 100, placeholder: "Enter a short bio here..." },
        { id: "signature", label: "Signature", type: "signature", x: 382, y: 340, width: 190, height: 60 },
      ]
    }],
  },
  {
    title: "Loco Trouble Report",
    description: "A detailed report for logging locomotive issues and resolutions.",
    category: "Reports",
    pageSetup: { format: 'letter', orientation: 'portrait' },
    pages: [{
      id: "page_loco_report_1",
      fields: [
        // Title
        { id: "title_1", type: "static-text", label: "Title", x: 40, y: 20, width: 532, height: 30, defaultValue: "Trouble Report", fontSize: 20, fontWeight: "bold", textAlign: "center", color: "#FF0000" },
        // Grid Lines
        { id: "h_line_1", type: "box", label: "h_line_1", x: 40, y: 50, width: 532, height: 2, color: "#000000" },
        { id: "h_line_2", type: "box", label: "h_line_2", x: 40, y: 80, width: 532, height: 1, color: "#000000" },
        { id: "h_line_3", type: "box", label: "h_line_3", x: 40, y: 150, width: 532, height: 1, color: "#000000" },
        { id: "h_line_4", type: "box", label: "h_line_4", x: 40, y: 250, width: 532, height: 1, color: "#000000" },
        { id: "h_line_5", type: "box", label: "h_line_5", x: 40, y: 400, width: 532, height: 1, color: "#000000" },
        { id: "h_line_6", type: "box", label: "h_line_6", x: 40, y: 550, width: 532, height: 1, color: "#000000" },
        { id: "h_line_7", type: "box", label: "h_line_7", x: 40, y: 650, width: 532, height: 1, color: "#000000" },
        { id: "h_line_8", type: "box", label: "h_line_8", x: 40, y: 750, width: 532, height: 2, color: "#000000" },
        { id: "v_line_1", type: "box", label: "v_line_1", x: 40, y: 50, width: 2, height: 702, color: "#000000" },
        { id: "v_line_2", type: "box", label: "v_line_2", x: 70, y: 50, width: 1, height: 600, color: "#000000" },
        { id: "v_line_3", type: "box", label: "v_line_3", x: 572, y: 50, width: 2, height: 702, color: "#000000" },
        { id: "v_line_row2_1", type: "box", label: "v_line_row2_1", x: 280, y: 80, width: 1, height: 70, color: "#000000" },
        { id: "v_line_row2_2", type: "box", label: "v_line_row2_2", x: 420, y: 80, width: 1, height: 70, color: "#000000" },
        { id: "v_line_sig", type: "box", label: "v_line_sig", x: 306, y: 650, width: 1, height: 100, color: "#000000" },
        { id: "h_line_row2_1", type: "box", label: "h_line_row2_1", x: 70, y: 100, width: 210, height: 1, color: "#000000" },
        { id: "h_line_row2_2", type: "box", label: "h_line_row2_2", x: 280, y: 100, width: 140, height: 1, color: "#000000" },
        { id: "h_line_row2_3", type: "box", label: "h_line_row2_3", x: 420, y: 115, width: 152, height: 1, color: "#000000" },
        { id: "h_line_row2_4", type: "box", label: "h_line_row2_4", x: 70, y: 125, width: 350, height: 1, color: "#000000" },
        // Content Fields
        { id: "num_1", type: "static-text", label: "1", x: 45, y: 58, width: 20, height: 20, defaultValue: "1", textAlign: "center" },
        { id: "num_2", type: "static-text", label: "2", x: 45, y: 105, width: 20, height: 20, defaultValue: "2", textAlign: "center" },
        { id: "num_3", type: "static-text", label: "3", x: 45, y: 155, width: 20, height: 20, defaultValue: "3", textAlign: "center" },
        { id: "num_4", type: "static-text", label: "4", x: 45, y: 255, width: 20, height: 20, defaultValue: "4", textAlign: "center" },
        { id: "num_5", type: "static-text", label: "5", x: 45, y: 405, width: 20, height: 20, defaultValue: "5", textAlign: "center" },
        { id: "num_6", type: "static-text", label: "6", x: 45, y: 555, width: 20, height: 20, defaultValue: "6", textAlign: "center" },
        { id: "date_label", type: "static-text", label: "Date Label", x: 75, y: 58, width: 40, height: 20, defaultValue: "Date:", groupId: "basic_info" },
        { id: "date_input", type: "date", label: "Date", x: 120, y: 52, width: 150, height: 25, groupId: "basic_info" },
        { id: "loco_label", type: "static-text", label: "Loco Label", x: 75, y: 83, width: 40, height: 20, defaultValue: "Loco:", groupId: "basic_info" },
        { id: "loco_input", type: "text", label: "Loco", x: 120, y: 82, width: 150, height: 18, groupId: "basic_info" },
        { id: "train_no_label", type: "static-text", label: "Train No Label", x: 75, y: 103, width: 60, height: 20, defaultValue: "Train No.:", groupId: "basic_info" },
        { id: "train_no_input", type: "text", label: "Train No.", x: 140, y: 102, width: 130, height: 18, groupId: "basic_info" },
        { id: "section_label", type: "static-text", label: "Section Label", x: 75, y: 128, width: 60, height: 20, defaultValue: "Section:", groupId: "basic_info" },
        { id: "section_input", type: "text", label: "Section", x: 140, y: 127, width: 130, height: 18, groupId: "basic_info" },
        { id: "shed_label", type: "static-text", label: "Shed Label", x: 285, y: 83, width: 40, height: 20, defaultValue: "Shed:", groupId: "basic_info" },
        { id: "shed_input", type: "text", label: "Shed", x: 330, y: 82, width: 80, height: 18, groupId: "basic_info" },
        { id: "load_label", type: "static-text", label: "Load Label", x: 285, y: 103, width: 40, height: 20, defaultValue: "Load:", groupId: "basic_info" },
        { id: "load_input", type: "text", label: "Load", x: 330, y: 102, width: 80, height: 18, groupId: "basic_info" },
        { id: "station_label", type: "static-text", label: "Station Label", x: 285, y: 128, width: 60, height: 20, defaultValue: "Station:", groupId: "basic_info" },
        { id: "station_input", type: "text", label: "Station", x: 350, y: 127, width: 60, height: 18, groupId: "basic_info" },
        { id: "sch_done_label", type: "static-text", label: "Sch Done Label", x: 425, y: 83, width: 70, height: 20, defaultValue: "Sch Done:", groupId: "basic_info" },
        { id: "sch_done_input", type: "text", label: "Sch Done", x: 500, y: 82, width: 65, height: 18, groupId: "basic_info" },
        { id: "lp_label", type: "static-text", label: "LP Label", x: 425, y: 118, width: 70, height: 20, defaultValue: "LP:", groupId: "basic_info" },
        { id: "lp_input", type: "text", label: "LP", x: 500, y: 117, width: 65, height: 18, groupId: "basic_info" },
        { id: "sch_due_label", type: "static-text", label: "Sch Due Label", x: 425, y: 100, width: 70, height: 20, defaultValue: "Sch Due:", groupId: "basic_info" },
        { id: "alp_label", type: "static-text", label: "ALP Label", x: 425, y: 132, width: 70, height: 20, defaultValue: "ALP:", groupId: "basic_info" },
        { id: "history_label", type: "static-text", label: "History Label", x: 75, y: 155, width: 100, height: 20, defaultValue: "Brief History:", groupId: "history" },
        { id: "history_input", type: "textarea", label: "Brief History", x: 75, y: 175, width: 490, height: 70, groupId: "history" },
        { id: "investigation_label", type: "static-text", label: "Investigation Label", x: 75, y: 255, width: 200, height: 20, defaultValue: "Investigation and Observation:", groupId: "investigation" },
        { id: "investigation_input", type: "textarea", label: "Investigation and Observation", x: 75, y: 275, width: 490, height: 120, groupId: "investigation" },
        { id: "conclusion_label", type: "static-text", label: "Conclusion Label", x: 75, y: 405, width: 100, height: 20, defaultValue: "Conclusion:", groupId: "conclusion" },
        { id: "conclusion_input", type: "textarea", label: "Conclusion", x: 75, y: 425, width: 490, height: 120, groupId: "conclusion" },
        { id: "responsibility_label", type: "static-text", label: "Responsibility Label", x: 75, y: 555, width: 100, height: 20, defaultValue: "Responsibility:", groupId: "responsibility" },
        { id: "responsibility_input", type: "textarea", label: "Responsibility", x: 75, y: 575, width: 490, height: 70, groupId: "responsibility" },
        { id: "sig_super_1", type: "signature", label: "Signature Supervisor 1", x: 45, y: 660, width: 255, height: 85, groupId: "signatures" },
        { id: "sig_super_2", type: "signature", label: "Signature Supervisor 2", x: 310, y: 660, width: 255, height: 85, groupId: "signatures" },
      ]
    }],
  }
];

import { templateStorage } from '@/utils/templateStorage';

export const getAllTemplates = async (): Promise<Template[]> => {
  try {
    const userTemplates = await templateStorage.getAllTemplates();
    const migratedUserTemplates = userTemplates.map(migrateTemplate);
    return [...templates, ...migratedUserTemplates];
  } catch (e) {
    console.error("Failed to load user templates:", e);
    return templates;
  }
};

// Synchronous version for backward compatibility
export const getAllTemplatesSync = (): Template[] => {
  try {
    const userTemplates: Template[] = JSON.parse(localStorage.getItem("user-templates") || "[]");
    const migratedUserTemplates = userTemplates.map(migrateTemplate);
    return [...templates, ...migratedUserTemplates.map(t => ({ ...t, isCustom: true }))];
  } catch (e) {
    console.error("Failed to load user templates:", e);
    return templates;
  }
};

export const getUniqueCategories = (): { category: string, subcategories: string[] }[] => {
  const allTemplates = getAllTemplatesSync();
  const categoryMap = new Map<string, Set<string>>();

  allTemplates.forEach(template => {
    if (!categoryMap.has(template.category)) {
      categoryMap.set(template.category, new Set());
    }
    if (template.subcategory) {
      categoryMap.get(template.category)!.add(template.subcategory);
    }
  });

  return Array.from(categoryMap.entries()).map(([category, subcategoriesSet]) => ({
    category,
    subcategories: Array.from(subcategoriesSet).sort(),
  }));
};

export const saveUserTemplate = async (template: Template): Promise<void> => {
  try {
    await templateStorage.saveTemplate(template);
  } catch (error) {
    console.error('Failed to save template:', error);
    throw new Error('Failed to save template. Storage may be unavailable.');
  }
};

// Synchronous version for backward compatibility
export const saveUserTemplateSync = (template: Template) => {
  try {
    const userTemplates: Template[] = JSON.parse(localStorage.getItem("user-templates") || "[]").map(migrateTemplate);
    userTemplates.push(template);
    localStorage.setItem("user-templates", JSON.stringify(userTemplates));
  } catch (error) {
    console.error('Failed to save template to localStorage:', error);
    throw new Error('Failed to save template. Storage may be full or unavailable.');
  }
};

export const updateUserTemplate = async (originalTitle: string, updatedTemplate: Template): Promise<void> => {
  try {
    await templateStorage.updateTemplate(originalTitle, updatedTemplate);
  } catch (error) {
    console.error('Failed to update template:', error);
    throw new Error('Failed to update template. Storage may be unavailable.');
  }
};

// Synchronous version for backward compatibility
export const updateUserTemplateSync = (originalTitle: string, updatedTemplate: Template) => {
  try {
    const userTemplates = getAllTemplatesSync().filter(t => t.isCustom);
    const index = userTemplates.findIndex(t => t.title === originalTitle);
    if (index !== -1) {
      userTemplates[index] = { ...updatedTemplate, isCustom: true };
      localStorage.setItem("user-templates", JSON.stringify(userTemplates));
    } else {
      throw new Error("Template to update not found.");
    }
  } catch (error) {
    console.error('Failed to update template in localStorage:', error);
    if (error instanceof Error && error.message === "Template to update not found.") {
      throw error;
    }
    throw new Error('Failed to update template. Storage may be full or unavailable.');
  }
};

export const deleteUserTemplate = async (title: string): Promise<void> => {
  try {
    await templateStorage.deleteTemplate(title);
  } catch (error) {
    console.error('Failed to delete template:', error);
    throw new Error('Failed to delete template. Storage may be unavailable.');
  }
};

// Synchronous version for backward compatibility
export const deleteUserTemplateSync = (title: string) => {
  try {
    let userTemplates: Template[] = JSON.parse(localStorage.getItem("user-templates") || "[]");
    userTemplates = userTemplates.filter(t => t.title !== title);
    localStorage.setItem("user-templates", JSON.stringify(userTemplates));
  } catch (error) {
    console.error('Failed to delete template from localStorage:', error);
    throw new Error('Failed to delete template. Storage may be unavailable.');
  }
};