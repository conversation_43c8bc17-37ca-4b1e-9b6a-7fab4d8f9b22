import * as pdfjsLib from 'pdfjs-dist';

// Set up PDF.js worker
if (typeof window !== 'undefined') {
  // Use CDN worker for production
  pdfjsLib.GlobalWorkerOptions.workerSrc = `//cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js`;
  
  // Alternative: Use local worker (uncomment if you want to bundle the worker)
  // pdfjsLib.GlobalWorkerOptions.workerSrc = new URL(
  //   'pdfjs-dist/build/pdf.worker.js',
  //   import.meta.url
  // ).toString();
}

export { pdfjsLib };
