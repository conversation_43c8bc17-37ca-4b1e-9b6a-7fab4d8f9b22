# Mobile App Setup Guide

## React Native Mobile App for Indian Railways Report Generator

### Prerequisites
- Node.js 18+
- React Native CLI or Expo CLI
- Android Studio (for Android development)
- Xcode (for iOS development, macOS only)

### Option 1: Expo (Recommended for beginners)

```bash
# Install Expo CLI
npm install -g @expo/cli

# Create new Expo project
npx create-expo-app IndianRailwaysMobile --template

# Navigate to project
cd IndianRailwaysMobile

# Install additional dependencies
npm install @supabase/supabase-js
npm install expo-document-picker
npm install expo-camera
npm install expo-file-system
npm install react-native-signature-canvas
npm install @react-navigation/native
npm install @react-navigation/stack
npm install react-native-paper
```

### Option 2: React Native CLI (More control)

```bash
# Install React Native CLI
npm install -g react-native-cli

# Create new React Native project
npx react-native init IndianRailwaysMobile

# Navigate to project
cd IndianRailwaysMobile

# Install dependencies
npm install @supabase/supabase-js
npm install react-native-document-picker
npm install react-native-camera
npm install react-native-fs
npm install react-native-signature-canvas
npm install @react-navigation/native
npm install @react-navigation/stack
npm install react-native-paper
```

### Key Mobile Features to Implement

#### 1. Document Scanner
```typescript
// DocumentScanner.tsx
import React from 'react';
import { Camera } from 'expo-camera';
import { View, TouchableOpacity, Text } from 'react-native';

export const DocumentScanner = () => {
  const [hasPermission, setHasPermission] = useState(null);
  const [type, setType] = useState(Camera.Constants.Type.back);

  useEffect(() => {
    (async () => {
      const { status } = await Camera.requestCameraPermissionsAsync();
      setHasPermission(status === 'granted');
    })();
  }, []);

  const takePicture = async () => {
    if (cameraRef.current) {
      const photo = await cameraRef.current.takePictureAsync();
      // Process the photo for OCR
      processDocument(photo.uri);
    }
  };

  return (
    <View style={{ flex: 1 }}>
      <Camera style={{ flex: 1 }} type={type} ref={cameraRef}>
        <TouchableOpacity onPress={takePicture}>
          <Text>Capture Document</Text>
        </TouchableOpacity>
      </Camera>
    </View>
  );
};
```

#### 2. Offline Storage
```typescript
// OfflineStorage.ts
import AsyncStorage from '@react-native-async-storage/async-storage';

export class OfflineStorage {
  static async saveTemplate(template: any) {
    try {
      await AsyncStorage.setItem(
        `template_${template.id}`, 
        JSON.stringify(template)
      );
    } catch (error) {
      console.error('Error saving template:', error);
    }
  }

  static async getTemplates() {
    try {
      const keys = await AsyncStorage.getAllKeys();
      const templateKeys = keys.filter(key => key.startsWith('template_'));
      const templates = await AsyncStorage.multiGet(templateKeys);
      return templates.map(([key, value]) => JSON.parse(value));
    } catch (error) {
      console.error('Error loading templates:', error);
      return [];
    }
  }

  static async syncWithServer() {
    // Sync offline data with Supabase when online
    const templates = await this.getTemplates();
    // Upload to Supabase...
  }
}
```

#### 3. Touch-Optimized Form
```typescript
// MobileForm.tsx
import React from 'react';
import { ScrollView, View } from 'react-native';
import { TextInput, Button } from 'react-native-paper';

export const MobileForm = ({ template, onSubmit }) => {
  const [formData, setFormData] = useState({});

  return (
    <ScrollView style={{ flex: 1, padding: 16 }}>
      {template.fields.map((field) => (
        <View key={field.id} style={{ marginBottom: 16 }}>
          <TextInput
            label={field.label}
            value={formData[field.id] || ''}
            onChangeText={(text) => 
              setFormData(prev => ({ ...prev, [field.id]: text }))
            }
            mode="outlined"
            multiline={field.type === 'textarea'}
            keyboardType={field.type === 'number' ? 'numeric' : 'default'}
          />
        </View>
      ))}
      <Button mode="contained" onPress={() => onSubmit(formData)}>
        Generate Report
      </Button>
    </ScrollView>
  );
};
```

#### 4. Push Notifications
```typescript
// NotificationService.ts
import * as Notifications from 'expo-notifications';

export class NotificationService {
  static async setupNotifications() {
    const { status } = await Notifications.requestPermissionsAsync();
    if (status !== 'granted') {
      alert('Failed to get push token for push notification!');
      return;
    }

    const token = (await Notifications.getExpoPushTokenAsync()).data;
    return token;
  }

  static async sendProcessingNotification(templateName: string) {
    await Notifications.scheduleNotificationAsync({
      content: {
        title: 'Template Processing Complete',
        body: `Your template "${templateName}" is ready to use!`,
        data: { templateName },
      },
      trigger: null, // Send immediately
    });
  }
}
```

### Mobile App Architecture

```
mobile-app/
├── src/
│   ├── components/
│   │   ├── DocumentScanner.tsx
│   │   ├── TemplateList.tsx
│   │   ├── MobileForm.tsx
│   │   └── SignatureCapture.tsx
│   ├── screens/
│   │   ├── HomeScreen.tsx
│   │   ├── UploadScreen.tsx
│   │   ├── TemplatesScreen.tsx
│   │   └── ReportsScreen.tsx
│   ├── services/
│   │   ├── OfflineStorage.ts
│   │   ├── NotificationService.ts
│   │   ├── CameraService.ts
│   │   └── SyncService.ts
│   ├── utils/
│   │   ├── OCRProcessor.ts
│   │   ├── DocumentProcessor.ts
│   │   └── ValidationUtils.ts
│   └── navigation/
│       └── AppNavigator.tsx
├── assets/
└── app.json
```

### Development Commands

```bash
# Start Expo development server
npm start

# Run on Android
npm run android

# Run on iOS (macOS only)
npm run ios

# Build for production
expo build:android
expo build:ios
```

### Key Mobile-Specific Features

1. **Camera Integration**: Scan documents with phone camera
2. **Offline Mode**: Work without internet connection
3. **Touch Gestures**: Swipe, pinch, tap interactions
4. **Push Notifications**: Real-time updates
5. **Biometric Auth**: Fingerprint/Face ID login
6. **Voice Input**: Speech-to-text for form filling
7. **GPS Integration**: Auto-fill location fields
8. **QR Code Scanner**: Quick template sharing

### Next Steps

1. Choose between Expo or React Native CLI
2. Set up development environment
3. Create basic navigation structure
4. Implement document scanner
5. Add offline storage
6. Integrate with existing backend
7. Test on real devices
8. Deploy to app stores
