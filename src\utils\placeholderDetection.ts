import mammoth from 'mammoth';
import { pdfjsLib } from './pdfWorkerSetup';

export interface DetectedPlaceholder {
  id: string;
  text: string;
  suggestedName: string;
  suggestedType: 'text' | 'textarea' | 'date' | 'number' | 'select' | 'signature' | 'email';
  confidence: number;
  position?: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
  context: string;
  detectionMethod: 'explicit' | 'blank-line' | 'form-field' | 'repeated-spaces';
}

export interface PlaceholderDetectionResult {
  placeholders: DetectedPlaceholder[];
  totalDetected: number;
  detectionMethod: 'explicit' | 'auto-detected' | 'mixed';
  documentText: string;
  documentStructure: any;
}

/**
 * Intelligent placeholder detection engine
 */
export class PlaceholderDetectionEngine {
  private railwayTerms = [
    'loco', 'locomotive', 'engine', 'train', 'station', 'division', 'branch',
    'kms', 'kilometer', 'track', 'signal', 'driver', 'guard', 'supervisor',
    'date', 'time', 'report', 'incident', 'maintenance', 'inspection',
    'number', 'no', 'serial', 'code', 'id', 'reference', 'ref'
  ];

  private contextPatterns = {
    date: /\b(date|dated|on|from|to|day|month|year|time)\b/i,
    number: /\b(no|number|serial|code|id|count|quantity|amount)\b/i,
    name: /\b(name|officer|driver|guard|supervisor|inspector|engineer)\b/i,
    location: /\b(station|depot|yard|shed|division|branch|section|km|kms)\b/i,
    signature: /\b(sign|signature|signed|approved|verified|checked)\b/i,
    email: /\b(email|mail|contact|correspondence)\b/i,
    description: /\b(description|details|remarks|notes|comments|observation)\b/i
  };

  /**
   * Detect placeholders from uploaded document
   */
  async detectPlaceholders(file: File): Promise<PlaceholderDetectionResult> {
    const fileType = file.type;
    let documentText = '';
    let documentStructure: any = {};

    try {
      if (fileType === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' || 
          file.name.endsWith('.docx')) {
        const result = await this.parseDocxFile(file);
        documentText = result.text;
        documentStructure = result.structure;
      } else if (fileType === 'application/pdf' || file.name.endsWith('.pdf')) {
        const result = await this.parsePdfFile(file);
        documentText = result.text;
        documentStructure = result.structure;
      } else {
        throw new Error('Unsupported file type. Please upload a DOCX or PDF file.');
      }

      const placeholders = this.analyzePlaceholders(documentText, documentStructure);
      
      return {
        placeholders,
        totalDetected: placeholders.length,
        detectionMethod: this.determineDetectionMethod(placeholders),
        documentText,
        documentStructure
      };
    } catch (error) {
      console.error('Error detecting placeholders:', error);
      throw new Error(`Failed to process document: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Parse DOCX file and extract text and structure
   */
  private async parseDocxFile(file: File): Promise<{ text: string; structure: any }> {
    const arrayBuffer = await file.arrayBuffer();
    const result = await mammoth.extractRawText({ arrayBuffer });
    
    // Also get HTML for structure analysis
    const htmlResult = await mammoth.convertToHtml({ arrayBuffer });
    
    return {
      text: result.value,
      structure: {
        html: htmlResult.value,
        messages: result.messages
      }
    };
  }

  /**
   * Parse PDF file and extract text and structure
   */
  private async parsePdfFile(file: File): Promise<{ text: string; structure: any }> {
    try {
      const arrayBuffer = await file.arrayBuffer();
      const pdf = await pdfjsLib.getDocument({ data: arrayBuffer }).promise;

      let fullText = '';
      const pages = [];

      // Extract text from each page
      for (let pageNum = 1; pageNum <= pdf.numPages; pageNum++) {
        const page = await pdf.getPage(pageNum);
        const textContent = await page.getTextContent();

        const pageText = textContent.items
          .map((item: any) => item.str)
          .join(' ');

        fullText += pageText + '\n';
        pages.push({
          pageNumber: pageNum,
          text: pageText
        });
      }

      return {
        text: fullText.trim(),
        structure: {
          pages: pdf.numPages,
          pageDetails: pages,
          info: pdf.info || {},
          metadata: pdf.metadata || {}
        }
      };
    } catch (error) {
      console.error('Error parsing PDF:', error);
      throw new Error('Failed to parse PDF file. The file may be corrupted or password-protected.');
    }
  }

  /**
   * Analyze text and detect placeholders using multiple methods
   */
  private analyzePlaceholders(text: string, structure: any): DetectedPlaceholder[] {
    const placeholders: DetectedPlaceholder[] = [];
    let idCounter = 1;

    // Method 1: Explicit placeholders ({{text}})
    const explicitPlaceholders = this.detectExplicitPlaceholders(text);
    placeholders.push(...explicitPlaceholders.map(p => ({
      ...p,
      id: `placeholder_${idCounter++}`
    })));

    // Method 2: Blank lines with underscores
    const blankLinePlaceholders = this.detectBlankLines(text);
    placeholders.push(...blankLinePlaceholders.map(p => ({
      ...p,
      id: `placeholder_${idCounter++}`
    })));

    // Method 3: Repeated spaces (form fields)
    const spacePlaceholders = this.detectRepeatedSpaces(text);
    placeholders.push(...spacePlaceholders.map(p => ({
      ...p,
      id: `placeholder_${idCounter++}`
    })));

    // Method 4: Context-based detection
    const contextPlaceholders = this.detectContextualPlaceholders(text);
    placeholders.push(...contextPlaceholders.map(p => ({
      ...p,
      id: `placeholder_${idCounter++}`
    })));

    // Remove duplicates and enhance with AI suggestions
    return this.deduplicateAndEnhance(placeholders);
  }

  /**
   * Detect explicit placeholders wrapped in double curly braces
   */
  private detectExplicitPlaceholders(text: string): Omit<DetectedPlaceholder, 'id'>[] {
    const regex = /\{\{([^}]+)\}\}/g;
    const placeholders: Omit<DetectedPlaceholder, 'id'>[] = [];
    let match;

    while ((match = regex.exec(text)) !== null) {
      const placeholderText = match[1].trim();
      const context = this.extractContext(text, match.index, 50);
      
      placeholders.push({
        text: match[0],
        suggestedName: this.generateSuggestedName(placeholderText, context),
        suggestedType: this.inferFieldType(placeholderText, context),
        confidence: 0.95,
        context,
        detectionMethod: 'explicit'
      });
    }

    return placeholders;
  }

  /**
   * Detect blank lines with underscores
   */
  private detectBlankLines(text: string): Omit<DetectedPlaceholder, 'id'>[] {
    const regex = /_{3,}/g;
    const placeholders: Omit<DetectedPlaceholder, 'id'>[] = [];
    let match;

    while ((match = regex.exec(text)) !== null) {
      const context = this.extractContext(text, match.index, 50);
      const suggestedName = this.generateContextualName(context);
      
      placeholders.push({
        text: match[0],
        suggestedName,
        suggestedType: this.inferFieldType(match[0], context),
        confidence: 0.8,
        context,
        detectionMethod: 'blank-line'
      });
    }

    return placeholders;
  }

  /**
   * Detect repeated spaces that might indicate form fields
   */
  private detectRepeatedSpaces(text: string): Omit<DetectedPlaceholder, 'id'>[] {
    const regex = /\s{5,}/g;
    const placeholders: Omit<DetectedPlaceholder, 'id'>[] = [];
    let match;

    while ((match = regex.exec(text)) !== null) {
      const context = this.extractContext(text, match.index, 50);
      
      // Skip if it's just paragraph spacing
      if (context.includes('\n\n') || context.includes('\r\n\r\n')) {
        continue;
      }

      const suggestedName = this.generateContextualName(context);
      
      placeholders.push({
        text: match[0],
        suggestedName,
        suggestedType: this.inferFieldType(match[0], context),
        confidence: 0.6,
        context,
        detectionMethod: 'repeated-spaces'
      });
    }

    return placeholders;
  }

  /**
   * Detect placeholders based on context patterns
   */
  private detectContextualPlaceholders(text: string): Omit<DetectedPlaceholder, 'id'>[] {
    const placeholders: Omit<DetectedPlaceholder, 'id'>[] = [];

    // Look for patterns like "Name: " followed by space or line break
    const patterns = [
      /([A-Za-z\s]+):\s*$/gm,
      /([A-Za-z\s]+):\s*\n/g,
      /([A-Za-z\s]+)\s*:\s*_+/g
    ];

    patterns.forEach(pattern => {
      let match;
      while ((match = pattern.exec(text)) !== null) {
        const label = match[1].trim();
        const context = this.extractContext(text, match.index, 50);

        if (this.isLikelyFieldLabel(label)) {
          placeholders.push({
            text: match[0],
            suggestedName: this.generateSuggestedName(label, context),
            suggestedType: this.inferFieldType(label, context),
            confidence: 0.7,
            context,
            detectionMethod: 'form-field'
          });
        }
      }
    });

    return placeholders;
  }

  /**
   * Extract context around a placeholder
   */
  private extractContext(text: string, position: number, length: number): string {
    const start = Math.max(0, position - length);
    const end = Math.min(text.length, position + length);
    return text.substring(start, end).trim();
  }

  /**
   * Generate suggested name based on placeholder text and context
   */
  private generateSuggestedName(placeholderText: string, context: string): string {
    // Clean up the placeholder text
    let name = placeholderText
      .replace(/[{}:_\s]+/g, ' ')
      .trim()
      .toLowerCase();

    // If empty, try to extract from context
    if (!name) {
      name = this.generateContextualName(context);
    }

    // Convert to camelCase
    return this.toCamelCase(name);
  }

  /**
   * Generate name from context
   */
  private generateContextualName(context: string): string {
    const words = context.toLowerCase().split(/\s+/);

    // Look for railway-specific terms
    for (const term of this.railwayTerms) {
      if (words.some(word => word.includes(term))) {
        const relevantWords = words.filter(word =>
          word.includes(term) ||
          this.isDescriptiveWord(word)
        ).slice(0, 3);

        if (relevantWords.length > 0) {
          return this.toCamelCase(relevantWords.join(' '));
        }
      }
    }

    // Fallback to generic field names
    const descriptiveWords = words.filter(word =>
      this.isDescriptiveWord(word) && word.length > 2
    ).slice(0, 2);

    return descriptiveWords.length > 0
      ? this.toCamelCase(descriptiveWords.join(' '))
      : 'customField';
  }

  /**
   * Check if a word is descriptive for field naming
   */
  private isDescriptiveWord(word: string): boolean {
    const stopWords = ['the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might', 'must', 'can', 'shall'];
    return !stopWords.includes(word.toLowerCase()) && /^[a-zA-Z]+$/.test(word);
  }

  /**
   * Convert string to camelCase
   */
  private toCamelCase(str: string): string {
    return str
      .replace(/(?:^\w|[A-Z]|\b\w)/g, (word, index) =>
        index === 0 ? word.toLowerCase() : word.toUpperCase()
      )
      .replace(/\s+/g, '');
  }

  /**
   * Infer field type based on placeholder text and context
   */
  private inferFieldType(placeholderText: string, context: string): DetectedPlaceholder['suggestedType'] {
    const combined = (placeholderText + ' ' + context).toLowerCase();

    // Check for specific patterns
    for (const [type, pattern] of Object.entries(this.contextPatterns)) {
      if (pattern.test(combined)) {
        switch (type) {
          case 'date': return 'date';
          case 'number': return 'number';
          case 'signature': return 'signature';
          case 'email': return 'email';
          case 'description': return 'textarea';
          default: return 'text';
        }
      }
    }

    // Default inference based on length and content
    if (placeholderText.length > 20 || combined.includes('remark') || combined.includes('detail')) {
      return 'textarea';
    }

    return 'text';
  }

  /**
   * Check if a label is likely a field label
   */
  private isLikelyFieldLabel(label: string): boolean {
    const minLength = 2;
    const maxLength = 50;

    if (label.length < minLength || label.length > maxLength) {
      return false;
    }

    // Check if it contains railway or form-related terms
    const lowerLabel = label.toLowerCase();
    const relevantTerms = [
      ...this.railwayTerms,
      'name', 'date', 'time', 'number', 'code', 'description', 'remarks',
      'signature', 'approved', 'verified', 'checked', 'officer', 'department'
    ];

    return relevantTerms.some(term => lowerLabel.includes(term)) ||
           /^[a-zA-Z\s]+$/.test(label); // Only letters and spaces
  }

  /**
   * Remove duplicates and enhance placeholders with AI suggestions
   */
  private deduplicateAndEnhance(placeholders: DetectedPlaceholder[]): DetectedPlaceholder[] {
    const seen = new Set<string>();
    const unique: DetectedPlaceholder[] = [];

    for (const placeholder of placeholders) {
      const key = `${placeholder.suggestedName}_${placeholder.text}`;

      if (!seen.has(key)) {
        seen.add(key);

        // Enhance with additional AI suggestions
        const enhanced = this.enhanceWithAI(placeholder);
        unique.push(enhanced);
      }
    }

    return unique.sort((a, b) => b.confidence - a.confidence);
  }

  /**
   * Enhance placeholder with AI-powered suggestions
   */
  private enhanceWithAI(placeholder: DetectedPlaceholder): DetectedPlaceholder {
    // Improve confidence based on detection method and context quality
    let confidence = placeholder.confidence;

    if (placeholder.detectionMethod === 'explicit') {
      confidence = Math.min(0.95, confidence + 0.1);
    }

    if (this.hasRailwayContext(placeholder.context)) {
      confidence = Math.min(0.9, confidence + 0.05);
    }

    // Improve suggested name if it's generic
    let suggestedName = placeholder.suggestedName;
    if (suggestedName === 'customField' || suggestedName.length < 3) {
      suggestedName = this.generateBetterName(placeholder.context, placeholder.text);
    }

    return {
      ...placeholder,
      confidence,
      suggestedName
    };
  }

  /**
   * Check if context contains railway-specific terms
   */
  private hasRailwayContext(context: string): boolean {
    const lowerContext = context.toLowerCase();
    return this.railwayTerms.some(term => lowerContext.includes(term));
  }

  /**
   * Generate a better name using advanced context analysis
   */
  private generateBetterName(context: string, placeholderText: string): string {
    const words = context.toLowerCase().split(/\s+/);

    // Look for position-based naming (before/after the placeholder)
    const beforeWords = context.substring(0, context.indexOf(placeholderText))
      .split(/\s+/)
      .filter(word => this.isDescriptiveWord(word))
      .slice(-2);

    const afterWords = context.substring(context.indexOf(placeholderText) + placeholderText.length)
      .split(/\s+/)
      .filter(word => this.isDescriptiveWord(word))
      .slice(0, 2);

    const relevantWords = [...beforeWords, ...afterWords].slice(0, 2);

    return relevantWords.length > 0
      ? this.toCamelCase(relevantWords.join(' '))
      : `field${Date.now().toString().slice(-4)}`;
  }

  /**
   * Determine the overall detection method used
   */
  private determineDetectionMethod(placeholders: DetectedPlaceholder[]): PlaceholderDetectionResult['detectionMethod'] {
    if (placeholders.length === 0) return 'auto-detected';

    const explicitCount = placeholders.filter(p => p.detectionMethod === 'explicit').length;
    const autoCount = placeholders.length - explicitCount;

    if (explicitCount > 0 && autoCount > 0) return 'mixed';
    if (explicitCount > 0) return 'explicit';
    return 'auto-detected';
  }
}
